2025-08-18 08:46:47,752 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:53] - Running with Spring Boot v2.1.4.RELEASE, Spring v5.1.6.RELEASE
2025-08-18 08:46:49,705 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.JwtAuthenticationFilter [L:241] - Filter 'jwtAuthenticationFilter' configured for use
2025-08-18 08:47:01,813 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:53] - Running with Spring Boot v2.1.4.RELEASE, Spring v5.1.6.RELEASE
2025-08-18 08:47:03,417 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.JwtAuthenticationFilter [L:241] - Filter 'jwtAuthenticationFilter' configured for use
2025-08-18 08:47:26,256 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/studentClass/queryAllClassNames from IP: 0:0:***
2025-08-18 08:47:26,256 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-9] c.o.a.ratelimit.RateLimitInterceptor [L:58] - RateLimitInterceptor - 无限流注解，放行请求: /bms/studentClass/queryAllClassNames
2025-08-18 08:47:51,714 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/auth/login from IP: 0:0:***
2025-08-18 08:47:51,715 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-10] c.o.a.ratelimit.RateLimitInterceptor [L:43] - RateLimitInterceptor - 非Controller方法，放行请求: /bms/auth/login
2025-08-18 08:47:51,726 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.ratelimit.RateLimitInterceptor [L:33] - RateLimitInterceptor - 处理请求: /bms/auth/login from IP: 0:0:***
2025-08-18 08:47:51,727 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.ratelimit.RateLimitInterceptor [L:62] - RateLimitInterceptor - 发现限流注解 GENERAL, 检查限流: /bms/auth/login
2025-08-18 08:47:51,729 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.ratelimit.RateLimitInterceptor [L:71] - RateLimitInterceptor - 限流检查通过，允许请求: /bms/auth/login
2025-08-18 08:47:51,931 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:48] - === 开始数据库用户认证流程 ===
2025-08-18 08:47:51,933 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:49] - 请求认证的用户名: ad***n
2025-08-18 08:47:52,021 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:58] - ✅ 找到用户: ad***n (ID: ***)
2025-08-18 08:47:52,022 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:59] - 用户编码: ADMIN001
2025-08-18 08:47:52,022 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:60] - 真实姓名: 系**
2025-08-18 08:47:52,022 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:61] - OA姓名: 系**
2025-08-18 08:47:52,022 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:62] - 用户状态: 1
2025-08-18 08:47:52,023 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:66] - 密码哈希长度: 60
2025-08-18 08:47:52,023 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:67] - 密码哈希格式: $2a$10$FxZ
2025-08-18 08:47:52,035 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:149] - 为用户 1 添加角色权限: ROLE_ADMIN
2025-08-18 08:47:52,039 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:126] - === DatabaseUserDetailsService处理完成 ===
2025-08-18 08:47:52,124 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.security.LoginRateLimiter [L:123] - 清除 IP 0:0:*** 和用户名 ad***n 的失败记录
2025-08-18 08:47:52,196 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.TokenManagementService [L:69] - 获取登录锁成功: username=admin
2025-08-18 08:47:52,207 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.TokenManagementService [L:75] - 单用户单Token策略：已清理用户 admin 的旧token
2025-08-18 08:47:52,220 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.TokenManagementService [L:92] - Token存储成功: username=admin, token前缀=eyJ0eXAiOiJKV1QiLCJh, 单token策略=true
2025-08-18 08:47:52,228 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.TokenManagementService [L:104] - 释放登录锁成功: username=admin
2025-08-18 08:47:52,236 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.TokenManagementService [L:386] - 获取登录锁成功，开始生成刷新令牌: username=admin
2025-08-18 08:47:52,243 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.TokenManagementService [L:410] - JWT Refresh Token生成成功: username=admin
2025-08-18 08:47:52,249 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.TokenManagementService [L:421] - 释放登录锁成功: username=admin
