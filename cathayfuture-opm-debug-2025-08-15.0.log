2025-08-15 15:54:38,807 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:53] - Running with Spring Boot v2.1.4.RELEASE, Spring v5.1.6.RELEASE
2025-08-15 15:54:40,496 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.JwtAuthenticationFilter [L:241] - Filter 'jwtAuthenticationFilter' configured for use
2025-08-15 16:10:26,743 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:53] - Running with Spring Boot v2.1.4.RELEASE, Spring v5.1.6.RELEASE
2025-08-15 16:10:28,762 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.JwtAuthenticationFilter [L:241] - Filter 'jwtAuthenticationFilter' configured for use
2025-08-15 16:31:10,166 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:53] - Running with Spring Boot v2.1.4.RELEASE, Spring v5.1.6.RELEASE
2025-08-15 16:31:12,022 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.JwtAuthenticationFilter [L:241] - Filter 'jwtAuthenticationFilter' configured for use
2025-08-15 17:04:26,079 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:53] - Running with Spring Boot v2.1.4.RELEASE, Spring v5.1.6.RELEASE
2025-08-15 17:04:27,780 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.JwtAuthenticationFilter [L:241] - Filter 'jwtAuthenticationFilter' configured for use
2025-08-15 17:41:38,657 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:53] - Running with Spring Boot v2.1.4.RELEASE, Spring v5.1.6.RELEASE
2025-08-15 17:41:40,496 DEBUG [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.s.JwtAuthenticationFilter [L:241] - Filter 'jwtAuthenticationFilter' configured for use
