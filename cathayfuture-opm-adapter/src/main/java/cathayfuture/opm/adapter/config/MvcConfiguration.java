package cathayfuture.opm.adapter.config;

import cathayfuture.opm.adapter.config.interceptor.WxAuthenticationInterceptor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class MvcConfiguration implements WebMvcConfigurer {

    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")
                .allowedOriginPatterns("*")  // 使用allowedOriginPatterns替代allowedOrigins以支持credentials
                .allowedMethods("GET", "HEAD", "POST", "PUT", "DELETE", "OPTIONS")
                .allowCredentials(true)
                .maxAge(3600)
                .allowedHeaders("*");
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 保留微信认证拦截器，但可以考虑迁移到Spring Security
        registry.addInterceptor(wxAuthenticationInterceptor())
                .addPathPatterns("/mobile/**")
                .excludePathPatterns("/mobile/wx/login", "/mobile/callbacks/**");
        // 移除ExtServiceContextInterceptor
    }

    @Bean
    public WxAuthenticationInterceptor wxAuthenticationInterceptor() {
        return new WxAuthenticationInterceptor();
    }
}

