package cathayfuture.opm.adapter.security;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.core.env.Environment;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.dao.DaoAuthenticationProvider;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;

import javax.annotation.PostConstruct;
import java.util.Arrays;

/**
 * 生产环境安全配置
 * 仅在生产环境（prod、prd）启用JWT认证
 * 
 * 注意：此类使用WebSecurityConfigurerAdapter，在Spring Security 5.7.0+中被弃用
 * 但在当前Spring Boot 2.1.4版本中这是推荐的做法
 */
@Slf4j
@Configuration
@EnableWebSecurity
@Profile({"prod", "test", "prd", "production"})
public class SecurityConfig extends WebSecurityConfigurerAdapter {

    private final DatabaseUserDetailsService databaseUserDetailsService;
    private final JwtAuthenticationFilter jwtAuthenticationFilter;
    private final Environment environment;
    
    @Value("${cathay.security.debug.enable-logging-provider:false}")
    private boolean enableLoggingProvider;

    public SecurityConfig(DatabaseUserDetailsService databaseUserDetailsService,
                         JwtAuthenticationFilter jwtAuthenticationFilter,
                         Environment environment) {
        this.databaseUserDetailsService = databaseUserDetailsService;
        this.jwtAuthenticationFilter = jwtAuthenticationFilter;
        this.environment = environment;
    }
    
    @PostConstruct
    public void logSecurityConfiguration() {
        log.info("=== 生产环境安全配置 ===");
        log.info("当前环境: {}", Arrays.toString(environment.getActiveProfiles()));
        log.info("认证方式: 数据库认证 ✅");
        log.info("详细认证日志: {}", enableLoggingProvider ? "启用" : "禁用");
        log.info("JWT认证: 启用 ✅");
        log.info("=======================");
    }
    
    @Override
    protected void configure(HttpSecurity http) throws Exception {
        log.info("✅ 配置生产环境安全策略：启用JWT认证");
        
        http
            .sessionManagement()
                .sessionCreationPolicy(SessionCreationPolicy.STATELESS)
            .and()
            .authorizeRequests()
                // 公开接口
                .antMatchers("/bms/auth/**").permitAll()
                .antMatchers("/mobile/wx/login", "/mobile/callbacks/**").permitAll()
                .antMatchers("/actuator/**").permitAll()
                .antMatchers("/swagger-ui/**", "/v3/api-docs/**", "/swagger-ui.html", "/webjars/**").permitAll()
                // 需要认证的接口
                .antMatchers("/bms/**").authenticated()
                .antMatchers("/mobile/**").authenticated()
                .anyRequest().authenticated()
            .and()
            .authenticationProvider(daoAuthenticationProvider())
            .addFilterBefore(jwtAuthenticationFilter, UsernamePasswordAuthenticationFilter.class)
            .csrf().disable()
            .cors();
        
        log.info("✅ 生产环境安全配置已生效");
    }
    
    @Bean
    public DaoAuthenticationProvider daoAuthenticationProvider() {
        if (enableLoggingProvider) {
            log.info("配置数据库认证提供者（生产环境，启用详细日志）");
            return new LoggingAuthenticationProvider(databaseUserDetailsService, passwordEncoder());
        } else {
            log.info("配置数据库认证提供者（生产环境，标准模式）");
            DaoAuthenticationProvider provider = new DaoAuthenticationProvider();
            provider.setUserDetailsService(databaseUserDetailsService);
            provider.setPasswordEncoder(passwordEncoder());
            return provider;
        }
    }
    
    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }
    
    @Bean
    @Override
    public AuthenticationManager authenticationManagerBean() throws Exception {
        return super.authenticationManagerBean();
    }
}