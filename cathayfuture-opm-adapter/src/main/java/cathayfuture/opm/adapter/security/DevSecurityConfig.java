package cathayfuture.opm.adapter.security;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.core.env.Environment;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.dao.DaoAuthenticationProvider;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;

import javax.annotation.PostConstruct;
import java.util.Arrays;

/**
 * 开发环境安全配置
 * 适用于dev、test、uat环境，所有接口无需认证
 * 
 * 注意：此类使用WebSecurityConfigurerAdapter，在Spring Security 5.7.0+中被弃用
 * 但在当前Spring Boot 2.1.4版本中这是推荐的做法
 */
@Slf4j
@Configuration
@EnableWebSecurity
@Profile({"dev", "uat", "local", "default"})
public class DevSecurityConfig extends WebSecurityConfigurerAdapter {

    private final DatabaseUserDetailsService databaseUserDetailsService;
    private final Environment environment;
    
    @Value("${cathay.security.debug.enable-logging-provider:false}")
    private boolean enableLoggingProvider;

    public DevSecurityConfig(DatabaseUserDetailsService databaseUserDetailsService,
                            Environment environment) {
        this.databaseUserDetailsService = databaseUserDetailsService;
        this.environment = environment;
    }
    
    @PostConstruct
    public void logSecurityConfiguration() {
        log.info("=== 开发环境安全配置 ===");
        log.info("当前环境: {}", Arrays.toString(environment.getActiveProfiles()));
        log.info("认证模式: 开发模式（无需认证）⚠️");
        log.info("数据库认证: 启用（仅用于登录接口）");
        log.info("详细认证日志: {}", enableLoggingProvider ? "启用" : "禁用");
        log.warn("⚠️  注意：所有接口均可无认证访问，请勿在生产环境使用");
        log.info("=======================");
    }
    
    @Override
    protected void configure(HttpSecurity http) throws Exception {
        log.warn("⚠️  配置开发环境安全策略：所有接口均可无认证访问");
        http
            .sessionManagement()
                .sessionCreationPolicy(SessionCreationPolicy.STATELESS)
            .and()
            .authorizeRequests()
                .anyRequest().permitAll()
            .and()
            .csrf().disable()
            .cors().disable();
        
        log.warn("⚠️  开发环境安全配置已生效，所有接口无需认证");
    }
    
    @Bean
    public DaoAuthenticationProvider daoAuthenticationProvider() {
        if (enableLoggingProvider) {
            log.info("配置数据库认证提供者（开发环境，启用详细日志）");
            LoggingAuthenticationProvider provider = new LoggingAuthenticationProvider(databaseUserDetailsService, passwordEncoder());
            return provider;
        } else {
            log.info("配置数据库认证提供者（开发环境，标准模式）");
            DaoAuthenticationProvider provider = new DaoAuthenticationProvider();
            provider.setUserDetailsService(databaseUserDetailsService);
            provider.setPasswordEncoder(passwordEncoder());
            return provider;
        }
    }
    
    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }
    
    @Bean
    @Override
    public AuthenticationManager authenticationManagerBean() throws Exception {
        return super.authenticationManagerBean();
    }
}