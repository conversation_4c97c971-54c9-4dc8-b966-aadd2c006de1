2025-08-18 08:46:47,749 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:50] - Starting App on chengzhx-MBP with PID 4768 (started by chengzhx in /Users/<USER>/Documents/work/tasly/project/华夏未来)
2025-08-18 08:46:47,752 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:679] - The following profiles are active: test
2025-08-18 08:46:48,630 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:244] - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-18 08:46:48,632 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:126] - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-08-18 08:46:48,675 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:182] - Finished Spring Data repository scanning in 34ms. Found 0 repository interfaces.
2025-08-18 08:46:48,948 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [L:330] - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$802be37f] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-18 08:46:49,098 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.e.tomcat.TomcatWebServer [L:90] - Tomcat initialized with port(s): 8080 (http)
2025-08-18 08:46:49,107 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.coyote.http11.Http11NioProtocol [L:173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-18 08:46:49,113 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardService [L:173] - Starting service [Tomcat]
2025-08-18 08:46:49,113 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardEngine [L:173] - Starting Servlet engine: [Apache Tomcat/9.0.17]
2025-08-18 08:46:49,181 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.c.c.C.[Tomcat].[localhost].[/] [L:173] - Initializing Spring embedded WebApplicationContext
2025-08-18 08:46:49,181 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.web.context.ContextLoader [L:296] - Root WebApplicationContext: initialization completed in 1406 ms
2025-08-18 08:46:49,509 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] org.redisson.Version [L:41] - Redisson 3.10.5
2025-08-18 08:46:49,646 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-6] o.r.c.p.MasterPubSubConnectionPool [L:168] - 1 connections initialized for ***********/***********:6379
2025-08-18 08:46:49,646 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-7] o.r.c.pool.MasterConnectionPool [L:168] - 1 connections initialized for ***********/***********:6379
2025-08-18 08:46:50,659 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:49] - === 开发环境安全配置 ===
2025-08-18 08:46:50,659 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:50] - 当前环境: [test]
2025-08-18 08:46:50,659 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:51] - 认证模式: 开发模式（无需认证）⚠️
2025-08-18 08:46:50,659 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:52] - 数据库认证: 启用（仅用于登录接口）
2025-08-18 08:46:50,659 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:53] - 详细认证日志: 禁用
2025-08-18 08:46:50,660 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:55] - =======================
2025-08-18 08:46:50,735 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] org.reflections.Reflections [L:232] - Reflections took 52 ms to scan 5 urls, producing 133 keys and 488 values 
2025-08-18 08:46:50,811 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.opm.adapter.security.BmsJwtUtils [L:99] - BMS JWT工具类初始化完成
2025-08-18 08:46:50,811 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.opm.adapter.security.BmsJwtUtils [L:100] - Access Token有效期: 24 小时
2025-08-18 08:46:50,811 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.opm.adapter.security.BmsJwtUtils [L:101] - Refresh Token有效期: 48 小时
2025-08-18 08:46:50,811 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.opm.adapter.security.BmsJwtUtils [L:102] - 发行人: CATHAY-FUTURE-BMS-SYSTEM
2025-08-18 08:46:50,811 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.opm.adapter.security.BmsJwtUtils [L:103] - 受众: CATHAY-FUTURE-BMS-SYSTEM
2025-08-18 08:46:50,925 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:81] - 配置数据库认证提供者（开发环境，标准模式）
2025-08-18 08:46:50,974 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.s.web.DefaultSecurityFilterChain [L:43] - Creating filter chain: any request, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@495da9a7, org.springframework.security.web.context.SecurityContextPersistenceFilter@7b8b07ae, org.springframework.security.web.header.HeaderWriterFilter@4e2824b1, org.springframework.security.web.authentication.logout.LogoutFilter@a55e82a, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@351d93bd, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@77856cc5, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@2beee3e8, org.springframework.security.web.session.SessionManagementFilter@566f4659, org.springframework.security.web.access.ExceptionTranslationFilter@579f3c8e, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@173a5fad]
2025-08-18 08:46:51,066 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.s.c.ThreadPoolTaskExecutor [L:171] - Initializing ExecutorService 'applicationTaskExecutor'
2025-08-18 08:46:51,360 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.a.e.web.EndpointLinksResolver [L:59] - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-08-18 08:46:51,429 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.coyote.http11.Http11NioProtocol [L:173] - Starting ProtocolHandler ["http-nio-8080"]
2025-08-18 08:46:51,437 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.e.tomcat.TomcatWebServer [L:204] - Tomcat started on port(s): 8080 (http) with context path ''
2025-08-18 08:46:51,439 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:59] - Started App in 4.033 seconds (JVM running for 4.607)
2025-08-18 08:46:51,664 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(3)-*************] com.zaxxer.hikari.HikariDataSource [L:110] - HikariPool-1 - Starting...
2025-08-18 08:46:51,667 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(6)-*************] o.a.c.c.C.[Tomcat].[localhost].[/] [L:173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-18 08:46:51,667 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(6)-*************] o.s.web.servlet.DispatcherServlet [L:524] - Initializing Servlet 'dispatcherServlet'
2025-08-18 08:46:51,673 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(6)-*************] o.s.web.servlet.DispatcherServlet [L:546] - Completed initialization in 6 ms
2025-08-18 08:46:51,825 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(3)-*************] com.zaxxer.hikari.HikariDataSource [L:123] - HikariPool-1 - Start completed.
2025-08-18 08:46:55,738 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [Thread-10] o.s.s.c.ThreadPoolTaskExecutor [L:208] - Shutting down ExecutorService 'applicationTaskExecutor'
2025-08-18 08:46:55,761 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [Thread-10] com.zaxxer.hikari.HikariDataSource [L:350] - HikariPool-1 - Shutdown initiated...
2025-08-18 08:46:55,764 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [Thread-10] com.zaxxer.hikari.HikariDataSource [L:352] - HikariPool-1 - Shutdown completed.
2025-08-18 08:47:01,812 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:50] - Starting App on chengzhx-MBP with PID 4800 (started by chengzhx in /Users/<USER>/Documents/work/tasly/project/华夏未来)
2025-08-18 08:47:01,813 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:679] - The following profiles are active: test
2025-08-18 08:47:02,460 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:244] - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-18 08:47:02,461 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:126] - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-08-18 08:47:02,499 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.d.r.c.RepositoryConfigurationDelegate [L:182] - Finished Spring Data repository scanning in 32ms. Found 0 repository interfaces.
2025-08-18 08:47:02,753 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [L:330] - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$3015e39c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-18 08:47:02,877 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.e.tomcat.TomcatWebServer [L:90] - Tomcat initialized with port(s): 8080 (http)
2025-08-18 08:47:02,885 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.coyote.http11.Http11NioProtocol [L:173] - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-18 08:47:02,890 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardService [L:173] - Starting service [Tomcat]
2025-08-18 08:47:02,890 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.catalina.core.StandardEngine [L:173] - Starting Servlet engine: [Apache Tomcat/9.0.17]
2025-08-18 08:47:02,944 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.c.c.C.[Tomcat].[localhost].[/] [L:173] - Initializing Spring embedded WebApplicationContext
2025-08-18 08:47:02,944 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.web.context.ContextLoader [L:296] - Root WebApplicationContext: initialization completed in 1113 ms
2025-08-18 08:47:03,247 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] org.redisson.Version [L:41] - Redisson 3.10.5
2025-08-18 08:47:03,366 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-7] o.r.c.pool.MasterConnectionPool [L:168] - 1 connections initialized for ***********/***********:6379
2025-08-18 08:47:03,366 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [redisson-netty-1-6] o.r.c.p.MasterPubSubConnectionPool [L:168] - 1 connections initialized for ***********/***********:6379
2025-08-18 08:47:04,249 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:49] - === 开发环境安全配置 ===
2025-08-18 08:47:04,250 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:50] - 当前环境: [test]
2025-08-18 08:47:04,250 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:51] - 认证模式: 开发模式（无需认证）⚠️
2025-08-18 08:47:04,250 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:52] - 数据库认证: 启用（仅用于登录接口）
2025-08-18 08:47:04,250 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:53] - 详细认证日志: 禁用
2025-08-18 08:47:04,250 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:55] - =======================
2025-08-18 08:47:04,311 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] org.reflections.Reflections [L:232] - Reflections took 44 ms to scan 5 urls, producing 133 keys and 488 values 
2025-08-18 08:47:04,378 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.opm.adapter.security.BmsJwtUtils [L:99] - BMS JWT工具类初始化完成
2025-08-18 08:47:04,378 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.opm.adapter.security.BmsJwtUtils [L:100] - Access Token有效期: 24 小时
2025-08-18 08:47:04,378 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.opm.adapter.security.BmsJwtUtils [L:101] - Refresh Token有效期: 48 小时
2025-08-18 08:47:04,378 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.opm.adapter.security.BmsJwtUtils [L:102] - 发行人: CATHAY-FUTURE-BMS-SYSTEM
2025-08-18 08:47:04,379 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.opm.adapter.security.BmsJwtUtils [L:103] - 受众: CATHAY-FUTURE-BMS-SYSTEM
2025-08-18 08:47:04,477 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] c.o.a.security.DevSecurityConfig [L:81] - 配置数据库认证提供者（开发环境，标准模式）
2025-08-18 08:47:04,520 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.s.web.DefaultSecurityFilterChain [L:43] - Creating filter chain: any request, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@3de88f64, org.springframework.security.web.context.SecurityContextPersistenceFilter@1d444652, org.springframework.security.web.header.HeaderWriterFilter@63b4b9c6, org.springframework.security.web.authentication.logout.LogoutFilter@42503099, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@1b1ea1d9, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@7d18338b, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@4e8afdad, org.springframework.security.web.session.SessionManagementFilter@5fb44964, org.springframework.security.web.access.ExceptionTranslationFilter@768f4b42, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@7c4ca87c]
2025-08-18 08:47:04,612 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.s.c.ThreadPoolTaskExecutor [L:171] - Initializing ExecutorService 'applicationTaskExecutor'
2025-08-18 08:47:04,894 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.a.e.web.EndpointLinksResolver [L:59] - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-08-18 08:47:04,963 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.a.coyote.http11.Http11NioProtocol [L:173] - Starting ProtocolHandler ["http-nio-8080"]
2025-08-18 08:47:04,970 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] o.s.b.w.e.tomcat.TomcatWebServer [L:204] - Tomcat started on port(s): 8080 (http) with context path ''
2025-08-18 08:47:04,971 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [main] cathayfuture.opm.App [L:59] - Started App in 3.485 seconds (JVM running for 3.777)
2025-08-18 08:47:05,457 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(3)-*************] com.zaxxer.hikari.HikariDataSource [L:110] - HikariPool-1 - Starting...
2025-08-18 08:47:05,458 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(4)-*************] o.a.c.c.C.[Tomcat].[localhost].[/] [L:173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-18 08:47:05,458 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(4)-*************] o.s.web.servlet.DispatcherServlet [L:524] - Initializing Servlet 'dispatcherServlet'
2025-08-18 08:47:05,466 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(4)-*************] o.s.web.servlet.DispatcherServlet [L:546] - Completed initialization in 7 ms
2025-08-18 08:47:05,661 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [RMI TCP Connection(3)-*************] com.zaxxer.hikari.HikariDataSource [L:123] - HikariPool-1 - Start completed.
2025-08-18 08:47:17,001 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-8] o.s.api.AbstractOpenApiResource [L:347] - Init duration for springdoc-openapi is: 733 ms
2025-08-18 08:47:52,023 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:73] - === 检查用户状态 ===
2025-08-18 08:47:52,025 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:75] - 用户是否启用: true
2025-08-18 08:47:52,025 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:83] - 账户是否锁定: false
2025-08-18 08:47:52,025 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:84] - 账户锁定时间: null
2025-08-18 08:47:52,025 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:91] - 密码是否过期: false
2025-08-18 08:47:52,025 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:92] - 密码更新时间: 2025-08-12T14:02:18
2025-08-18 08:47:52,025 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:98] - === 加载用户权限 ===
2025-08-18 08:47:52,036 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:103] - 用户权限列表: [ROLE_ADMIN]
2025-08-18 08:47:52,036 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:104] - 权限数量: 1
2025-08-18 08:47:52,036 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:107] - === 构建UserDetails对象 ===
2025-08-18 08:47:52,036 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:108] - 用户名: admin
2025-08-18 08:47:52,036 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:109] - 密码哈希: $2a$10$FxZeSFOP6xUwUSL5YvJuZ.rh6hLN.rKWP0qMhsDT2votCEdhvG6Yq
2025-08-18 08:47:52,036 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:110] - 账户过期: false
2025-08-18 08:47:52,036 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:111] - 账户锁定: false
2025-08-18 08:47:52,037 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:112] - 凭据过期: false
2025-08-18 08:47:52,037 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:113] - 账户禁用: false
2025-08-18 08:47:52,038 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [http-nio-8080-exec-1] c.o.a.s.DatabaseUserDetailsService [L:125] - ✅ 用户 ad***n 认证信息加载完成
2025-08-18 08:47:55,874 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [Thread-9] o.s.s.c.ThreadPoolTaskExecutor [L:208] - Shutting down ExecutorService 'applicationTaskExecutor'
2025-08-18 08:47:55,899 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [Thread-9] com.zaxxer.hikari.HikariDataSource [L:350] - HikariPool-1 - Shutdown initiated...
2025-08-18 08:47:55,901 INFO  [v:1.0] [h:127.0.0.1] [m:cathayfuture-opm] [reqId:] [Thread-9] com.zaxxer.hikari.HikariDataSource [L:352] - HikariPool-1 - Shutdown completed.
